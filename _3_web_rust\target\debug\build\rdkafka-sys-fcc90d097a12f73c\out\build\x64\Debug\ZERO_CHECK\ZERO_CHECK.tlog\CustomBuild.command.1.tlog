^D:\<PERSON><PERSON><PERSON>SEARCH\240924_KAFKA_FLINK_RUST_HTML最新成交价监测@CLAW日本145.242\FLINK_RUST_TRADING\_3_WEB_RUST\TARGET\DEBUG\BUILD\RDKAFKA-SYS-FCC90D097A12F73C\OUT\BUILD\CMAKEFILES\74549D8DFB731C2632ACD3DDC35E5622\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rdkafka-sys-4.9.0+2.10.0/librdkafka -BD:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build/RdKafka.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
