﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\crc32c.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdaddr.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdavl.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdbuf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdcrc32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdfnv1a.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdbase64.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_assignor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_broker.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_buf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_cgrp.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_conf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_event.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_feature.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_lz4.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_metadata.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_metadata_cache.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_msg.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_msgset_reader.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_msgset_writer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_offset.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_op.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_partition.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_pattern.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_queue.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_range_assignor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_request.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_roundrobin_assignor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_sasl.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_sasl_plain.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_sticky_assignor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_subscription.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_assignment.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_timer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_topic.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_transport.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_interceptor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_header.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_admin.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_aux.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_background.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_idempotence.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_txnmgr.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_cert.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_coord.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_mock.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_mock_handlers.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_mock_cgrp.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_error.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_fetcher.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_telemetry.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_telemetry_decode.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_telemetry_encode.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\nanopb\pb_encode.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\nanopb\pb_decode.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\nanopb\pb_common.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\opentelemetry\metrics.pb.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\opentelemetry\common.pb.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\opentelemetry\resource.pb.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdlist.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdlog.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdmurmur2.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdports.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdrand.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdregex.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdstring.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdunittest.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdvarint.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdmap.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\snappy.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\tinycthread.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\tinycthread_extra.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdxxhash.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\cJSON.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rddl.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_plugin.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_sasl_win32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdgz.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\lz4.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\lz4frame.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\lz4hc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\regexp.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{313A0486-2FCE-323E-8462-F7D11824A9D0}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
