{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[17022423707615322322, "build_script_build", false, 14346840873628913588]], "local": [{"RerunIfChanged": {"output": "debug\\build\\libz-sys-0f661d1bf475f385\\output", "paths": ["build.rs", "zng/cmake.rs", "zng/cc.rs"]}}, {"RerunIfEnvChanged": {"var": "LIBZ_SYS_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "VCINSTALLDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "VSTEL_MSBuildProjectFullPath", "val": null}}, {"RerunIfEnvChanged": {"var": "VSCMD_ARG_VCVARS_SPECTRE", "val": null}}, {"RerunIfEnvChanged": {"var": "WindowsSdkDir", "val": null}}, {"RerunIfEnvChanged": {"var": "WindowsSDKVersion", "val": null}}, {"RerunIfEnvChanged": {"var": "LIB", "val": null}}, {"RerunIfEnvChanged": {"var": "INCLUDE", "val": "C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.43.34808\\include;C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\Windows Kits\\10\\Include\\10.0.22621.0\\ucrt;C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\Windows Kits\\10\\Include\\10.0.22621.0\\um;C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\Windows Kits\\10\\Include\\10.0.22621.0\\shared"}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_AR", "val": null}}, {"RerunIfEnvChanged": {"var": "AR", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64-pc-windows-msvc", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}