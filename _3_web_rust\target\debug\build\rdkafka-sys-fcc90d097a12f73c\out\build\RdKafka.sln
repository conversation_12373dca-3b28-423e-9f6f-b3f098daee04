﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{007014CF-BF71-3C84-889F-F7BA5B189BA6}"
	ProjectSection(ProjectDependencies) = postProject
		{780C57D4-C24F-3321-9A34-FD92604DF167} = {780C57D4-C24F-3321-9A34-FD92604DF167}
		{D837848D-C883-3685-B315-330DCA02EDB4} = {D837848D-C883-3685-B315-330DCA02EDB4}
		{552DF33B-2A52-31B1-BCCC-C5517ECC68EB} = {552DF33B-2A52-31B1-BCCC-C5517ECC68EB}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{A601198D-E6DC-3189-ADA5-D7ADEDCBA33F}"
	ProjectSection(ProjectDependencies) = postProject
		{007014CF-BF71-3C84-889F-F7BA5B189BA6} = {007014CF-BF71-3C84-889F-F7BA5B189BA6}
		{780C57D4-C24F-3321-9A34-FD92604DF167} = {780C57D4-C24F-3321-9A34-FD92604DF167}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{780C57D4-C24F-3321-9A34-FD92604DF167}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "rdkafka", "src\rdkafka.vcxproj", "{D837848D-C883-3685-B315-330DCA02EDB4}"
	ProjectSection(ProjectDependencies) = postProject
		{780C57D4-C24F-3321-9A34-FD92604DF167} = {780C57D4-C24F-3321-9A34-FD92604DF167}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "rdkafka++", "src-cpp\rdkafka++.vcxproj", "{552DF33B-2A52-31B1-BCCC-C5517ECC68EB}"
	ProjectSection(ProjectDependencies) = postProject
		{780C57D4-C24F-3321-9A34-FD92604DF167} = {780C57D4-C24F-3321-9A34-FD92604DF167}
		{D837848D-C883-3685-B315-330DCA02EDB4} = {D837848D-C883-3685-B315-330DCA02EDB4}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{007014CF-BF71-3C84-889F-F7BA5B189BA6}.Debug|x64.ActiveCfg = Debug|x64
		{007014CF-BF71-3C84-889F-F7BA5B189BA6}.Debug|x64.Build.0 = Debug|x64
		{007014CF-BF71-3C84-889F-F7BA5B189BA6}.Release|x64.ActiveCfg = Release|x64
		{007014CF-BF71-3C84-889F-F7BA5B189BA6}.Release|x64.Build.0 = Release|x64
		{007014CF-BF71-3C84-889F-F7BA5B189BA6}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{007014CF-BF71-3C84-889F-F7BA5B189BA6}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{007014CF-BF71-3C84-889F-F7BA5B189BA6}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{007014CF-BF71-3C84-889F-F7BA5B189BA6}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{A601198D-E6DC-3189-ADA5-D7ADEDCBA33F}.Debug|x64.ActiveCfg = Debug|x64
		{A601198D-E6DC-3189-ADA5-D7ADEDCBA33F}.Release|x64.ActiveCfg = Release|x64
		{A601198D-E6DC-3189-ADA5-D7ADEDCBA33F}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{A601198D-E6DC-3189-ADA5-D7ADEDCBA33F}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{780C57D4-C24F-3321-9A34-FD92604DF167}.Debug|x64.ActiveCfg = Debug|x64
		{780C57D4-C24F-3321-9A34-FD92604DF167}.Debug|x64.Build.0 = Debug|x64
		{780C57D4-C24F-3321-9A34-FD92604DF167}.Release|x64.ActiveCfg = Release|x64
		{780C57D4-C24F-3321-9A34-FD92604DF167}.Release|x64.Build.0 = Release|x64
		{780C57D4-C24F-3321-9A34-FD92604DF167}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{780C57D4-C24F-3321-9A34-FD92604DF167}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{780C57D4-C24F-3321-9A34-FD92604DF167}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{780C57D4-C24F-3321-9A34-FD92604DF167}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{D837848D-C883-3685-B315-330DCA02EDB4}.Debug|x64.ActiveCfg = Debug|x64
		{D837848D-C883-3685-B315-330DCA02EDB4}.Debug|x64.Build.0 = Debug|x64
		{D837848D-C883-3685-B315-330DCA02EDB4}.Release|x64.ActiveCfg = Release|x64
		{D837848D-C883-3685-B315-330DCA02EDB4}.Release|x64.Build.0 = Release|x64
		{D837848D-C883-3685-B315-330DCA02EDB4}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{D837848D-C883-3685-B315-330DCA02EDB4}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{D837848D-C883-3685-B315-330DCA02EDB4}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{D837848D-C883-3685-B315-330DCA02EDB4}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{552DF33B-2A52-31B1-BCCC-C5517ECC68EB}.Debug|x64.ActiveCfg = Debug|x64
		{552DF33B-2A52-31B1-BCCC-C5517ECC68EB}.Debug|x64.Build.0 = Debug|x64
		{552DF33B-2A52-31B1-BCCC-C5517ECC68EB}.Release|x64.ActiveCfg = Release|x64
		{552DF33B-2A52-31B1-BCCC-C5517ECC68EB}.Release|x64.Build.0 = Release|x64
		{552DF33B-2A52-31B1-BCCC-C5517ECC68EB}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{552DF33B-2A52-31B1-BCCC-C5517ECC68EB}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{552DF33B-2A52-31B1-BCCC-C5517ECC68EB}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{552DF33B-2A52-31B1-BCCC-C5517ECC68EB}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {4B7EEAA1-9D53-3559-BCF2-E55A1F9A8A93}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
