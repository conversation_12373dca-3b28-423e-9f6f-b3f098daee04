﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{D837848D-C883-3685-B315-330DCA02EDB4}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>rdkafka</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\src\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">rdkafka.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">rdkafka</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\src\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">rdkafka.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">rdkafka</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\src\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">rdkafka.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">rdkafka</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\src\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">rdkafka.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">rdkafka</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/libz-sys-0f661d1bf475f385/out/include" -Brepro -Brepro</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>
      </ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WITHOUT_WIN32_CONFIG;WITH_SSL=0;WITH_ZLIB=1;WITH_SNAPPY=0;WITH_ZSTD=0;WITH_SASL_SCRAM=0;WITH_SASL_OAUTHBEARER=0;ENABLE_DEVEL=0;WITH_PLUGINS=1;LIBRDKAFKA_STATICLIB;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;WITHOUT_WIN32_CONFIG;WITH_SSL=0;WITH_ZLIB=1;WITH_SNAPPY=0;WITH_ZSTD=0;WITH_SASL_SCRAM=0;WITH_SASL_OAUTHBEARER=0;ENABLE_DEVEL=0;WITH_PLUGINS=1;LIBRDKAFKA_STATICLIB;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\libz-sys-0f661d1bf475f385\out\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\libz-sys-0f661d1bf475f385\out\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/libz-sys-0f661d1bf475f385/out/include" -Brepro</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>
      </ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);NDEBUG;WITHOUT_WIN32_CONFIG;WITH_SSL=0;WITH_ZLIB=1;WITH_SNAPPY=0;WITH_ZSTD=0;WITH_SASL_SCRAM=0;WITH_SASL_OAUTHBEARER=0;ENABLE_DEVEL=0;WITH_PLUGINS=1;LIBRDKAFKA_STATICLIB;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;NDEBUG;WITHOUT_WIN32_CONFIG;WITH_SSL=0;WITH_ZLIB=1;WITH_SNAPPY=0;WITH_ZSTD=0;WITH_SASL_SCRAM=0;WITH_SASL_OAUTHBEARER=0;ENABLE_DEVEL=0;WITH_PLUGINS=1;LIBRDKAFKA_STATICLIB;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\libz-sys-0f661d1bf475f385\out\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\libz-sys-0f661d1bf475f385\out\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/libz-sys-0f661d1bf475f385/out/include" -Brepro</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>
      </ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);NDEBUG;WITHOUT_WIN32_CONFIG;WITH_SSL=0;WITH_ZLIB=1;WITH_SNAPPY=0;WITH_ZSTD=0;WITH_SASL_SCRAM=0;WITH_SASL_OAUTHBEARER=0;ENABLE_DEVEL=0;WITH_PLUGINS=1;LIBRDKAFKA_STATICLIB;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;NDEBUG;WITHOUT_WIN32_CONFIG;WITH_SSL=0;WITH_ZLIB=1;WITH_SNAPPY=0;WITH_ZSTD=0;WITH_SASL_SCRAM=0;WITH_SASL_OAUTHBEARER=0;ENABLE_DEVEL=0;WITH_PLUGINS=1;LIBRDKAFKA_STATICLIB;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\libz-sys-0f661d1bf475f385\out\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\libz-sys-0f661d1bf475f385\out\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/libz-sys-0f661d1bf475f385/out/include" -Brepro</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>
      </ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);NDEBUG;WITHOUT_WIN32_CONFIG;WITH_SSL=0;WITH_ZLIB=1;WITH_SNAPPY=0;WITH_ZSTD=0;WITH_SASL_SCRAM=0;WITH_SASL_OAUTHBEARER=0;ENABLE_DEVEL=0;WITH_PLUGINS=1;LIBRDKAFKA_STATICLIB;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;NDEBUG;WITHOUT_WIN32_CONFIG;WITH_SSL=0;WITH_ZLIB=1;WITH_SNAPPY=0;WITH_ZSTD=0;WITH_SASL_SCRAM=0;WITH_SASL_OAUTHBEARER=0;ENABLE_DEVEL=0;WITH_PLUGINS=1;LIBRDKAFKA_STATICLIB;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\libz-sys-0f661d1bf475f385\out\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\libz-sys-0f661d1bf475f385\out\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rdkafka-sys-4.9.0*****.0/librdkafka/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rdkafka-sys-4.9.0*****.0/librdkafka -BD:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build --check-stamp-file D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-3.25\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\packaging\cmake\rdkafka.pc.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rdkafka-sys-4.9.0*****.0/librdkafka/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rdkafka-sys-4.9.0*****.0/librdkafka -BD:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build --check-stamp-file D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-3.25\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\packaging\cmake\rdkafka.pc.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rdkafka-sys-4.9.0*****.0/librdkafka/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rdkafka-sys-4.9.0*****.0/librdkafka -BD:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build --check-stamp-file D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-3.25\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\packaging\cmake\rdkafka.pc.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rdkafka-sys-4.9.0*****.0/librdkafka/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rdkafka-sys-4.9.0*****.0/librdkafka -BD:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build --check-stamp-file D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-3.25\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\packaging\cmake\rdkafka.pc.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\crc32c.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdaddr.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdavl.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdbuf.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdcrc32.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdfnv1a.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdbase64.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_assignor.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_broker.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_buf.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_cgrp.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_conf.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_event.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_feature.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_lz4.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_metadata.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_metadata_cache.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_msg.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_msgset_reader.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_msgset_writer.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_offset.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_op.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_partition.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_pattern.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_queue.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_range_assignor.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_request.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_roundrobin_assignor.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_sasl.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_sasl_plain.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_sticky_assignor.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_subscription.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_assignment.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_timer.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_topic.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_transport.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_interceptor.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_header.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_admin.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_aux.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_background.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_idempotence.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_txnmgr.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_cert.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_coord.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_mock.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_mock_handlers.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_mock_cgrp.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_error.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_fetcher.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_telemetry.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_telemetry_decode.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_telemetry_encode.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\nanopb\pb_encode.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\nanopb\pb_decode.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\nanopb\pb_common.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\opentelemetry\metrics.pb.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\opentelemetry\common.pb.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\opentelemetry\resource.pb.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdlist.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdlog.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdmurmur2.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdports.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdrand.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdregex.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdstring.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdunittest.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdvarint.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdmap.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\snappy.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\tinycthread.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\tinycthread_extra.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdxxhash.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\cJSON.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rddl.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_plugin.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdkafka_sasl_win32.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\rdgz.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\lz4.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\lz4frame.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\lz4hc.c" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0*****.0\librdkafka\src\regexp.c" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\ZERO_CHECK.vcxproj">
      <Project>{780C57D4-C24F-3321-9A34-FD92604DF167}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>