{"rustc": 1842507548689473721, "features": "[\"data-encoding\", \"handshake\", \"http\", \"httparse\", \"sha1\", \"url\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 1270341572213479472, "profile": 2241668132362809309, "path": 11384101578396505487, "deps": [[99287295355353247, "data_encoding", false, 4738411078802825455], [3150220818285335163, "url", false, 4087522642502945109], [3712811570531045576, "byteorder", false, 3482604888718664147], [4359956005902820838, "utf8", false, 13003836904205721433], [5986029879202738730, "log", false, 8612627211266642020], [6163892036024256188, "httparse", false, 7982990163492455792], [8008191657135824715, "thiserror", false, 8776407991565311630], [9010263965687315507, "http", false, 15083974679218805705], [10724389056617919257, "sha1", false, 16024587607982748342], [13208667028893622512, "rand", false, 10734983754643232354], [16066129441945555748, "bytes", false, 3429117579287067157]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tungstenite-c7f9ced0783e210a\\dep-lib-tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}