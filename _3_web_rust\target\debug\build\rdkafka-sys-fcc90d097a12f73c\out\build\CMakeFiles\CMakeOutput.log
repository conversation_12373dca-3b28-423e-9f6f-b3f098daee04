The system is: Windows - 10.0.26100 - AMD64
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler:  
Build flags: ;-nologo;-MD;-Brepro
Id flags:  

The output was:
0
閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
鐢熸垚鍚姩鏃堕棿涓?2025-08-04 11:03:33銆?

鑺傜偣 1 涓婄殑椤圭洰鈥淒:\DeepResearch\240924_Kafka_Flink_Rust_Html鏈€鏂版垚浜や环鐩戞祴@CLAW鏃ユ湰145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\CMakeFiles\3.25.0\CompilerIdC\CompilerIdC.vcxproj鈥?榛樿鐩爣)銆?
PrepareForBuild:
  姝ｅ湪鍒涘缓鐩綍鈥淒ebug\鈥濄€?
  宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
  姝ｅ湪鍒涘缓鐩綍鈥淒ebug\CompilerIdC.tlog\鈥濄€?
InitializeBuildStatus:
  姝ｅ湪鍒涘缓鈥淒ebug\CompilerIdC.tlog\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
  姝ｅ湪瀵光€淒ebug\CompilerIdC.tlog\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
ClCompile:
  C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\" /Fd"Debug\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
  CMakeCCompilerId.c
Link:
  C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe /ERRORREPORT:QUEUE /OUT:".\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\CompilerIdC.lib" /MACHINE:X64 Debug\CMakeCCompilerId.obj
  CompilerIdC.vcxproj -> D:\DeepResearch\240924_Kafka_Flink_Rust_Html鏈€鏂版垚浜や环鐩戞祴@CLAW鏃ユ湰145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\CMakeFiles\3.25.0\CompilerIdC\CompilerIdC.exe
PostBuildEvent:
  for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
  :VCEnd
  CMAKE_C_COMPILER=C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\cl.exe
FinalizeBuildStatus:
  姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\CompilerIdC.tlog\unsuccessfulbuild鈥濄€?
  姝ｅ湪瀵光€淒ebug\CompilerIdC.tlog\CompilerIdC.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
宸插畬鎴愮敓鎴愰」鐩€淒:\DeepResearch\240924_Kafka_Flink_Rust_Html鏈€鏂版垚浜や环鐩戞祴@CLAW鏃ユ湰145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\CMakeFiles\3.25.0\CompilerIdC\CompilerIdC.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?

宸叉垚鍔熺敓鎴愩€?
    0 涓鍛?
    0 涓敊璇?

宸茬敤鏃堕棿 00:00:00.59


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"

Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"

The C compiler identification is MSVC, found in "D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build/CMakeFiles/3.25.0/CompilerIdC/CompilerIdC.exe"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler:  
Build flags: ;-nologo;-MD;-Brepro
Id flags:  

The output was:
0
閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
鐢熸垚鍚姩鏃堕棿涓?2025-08-04 11:03:34銆?

鑺傜偣 1 涓婄殑椤圭洰鈥淒:\DeepResearch\240924_Kafka_Flink_Rust_Html鏈€鏂版垚浜や环鐩戞祴@CLAW鏃ユ湰145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\CMakeFiles\3.25.0\CompilerIdCXX\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)銆?
PrepareForBuild:
  姝ｅ湪鍒涘缓鐩綍鈥淒ebug\鈥濄€?
  宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
  姝ｅ湪鍒涘缓鐩綍鈥淒ebug\CompilerIdCXX.tlog\鈥濄€?
InitializeBuildStatus:
  姝ｅ湪鍒涘缓鈥淒ebug\CompilerIdCXX.tlog\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
  姝ｅ湪瀵光€淒ebug\CompilerIdCXX.tlog\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
ClCompile:
  C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\" /Fd"Debug\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
  CMakeCXXCompilerId.cpp
Link:
  C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe /ERRORREPORT:QUEUE /OUT:".\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\CompilerIdCXX.lib" /MACHINE:X64 Debug\CMakeCXXCompilerId.obj
  CompilerIdCXX.vcxproj -> D:\DeepResearch\240924_Kafka_Flink_Rust_Html鏈€鏂版垚浜や环鐩戞祴@CLAW鏃ユ湰145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\CMakeFiles\3.25.0\CompilerIdCXX\CompilerIdCXX.exe
PostBuildEvent:
  for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
  :VCEnd
  CMAKE_CXX_COMPILER=C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\cl.exe
FinalizeBuildStatus:
  姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\CompilerIdCXX.tlog\unsuccessfulbuild鈥濄€?
  姝ｅ湪瀵光€淒ebug\CompilerIdCXX.tlog\CompilerIdCXX.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
宸插畬鎴愮敓鎴愰」鐩€淒:\DeepResearch\240924_Kafka_Flink_Rust_Html鏈€鏂版垚浜や环鐩戞祴@CLAW鏃ユ湰145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\CMakeFiles\3.25.0\CompilerIdCXX\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?

宸叉垚鍔熺敓鎴愩€?
    0 涓鍛?
    0 涓敊璇?

宸茬敤鏃堕棿 00:00:01.23


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"

Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"

The CXX compiler identification is MSVC, found in "D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build/CMakeFiles/3.25.0/CompilerIdCXX/CompilerIdCXX.exe"

Detecting C compiler ABI info compiled with the following output:
Change Dir: D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build/CMakeFiles/CMakeScratch/TryCompile-5dhm9c

Run Build Command(s):C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_6e65a.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:m && 閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1



  CMakeCCompilerABI.c

  cmTC_6e65a.vcxproj -> D:\DeepResearch\240924_Kafka_Flink_Rust_Html鏈€鏂版垚浜や环鐩戞祴@CLAW鏃ユ湰145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\CMakeFiles\CMakeScratch\TryCompile-5dhm9c\Debug\cmTC_6e65a.exe




