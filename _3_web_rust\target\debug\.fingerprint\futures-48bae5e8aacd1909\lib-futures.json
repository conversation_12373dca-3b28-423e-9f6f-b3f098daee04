{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 17467636112133979524, "path": 3482657298681496565, "deps": [[5103565458935487, "futures_io", false, 2736107132049898552], [1811549171721445101, "futures_channel", false, 1601385856298452114], [7013762810557009322, "futures_sink", false, 17253566161847142577], [7620660491849607393, "futures_core", false, 9701341627058915714], [10629569228670356391, "futures_util", false, 9927367081065934677], [12779779637805422465, "futures_executor", false, 7293587945298342864], [16240732885093539806, "futures_task", false, 1502256611090412788]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-48bae5e8aacd1909\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}