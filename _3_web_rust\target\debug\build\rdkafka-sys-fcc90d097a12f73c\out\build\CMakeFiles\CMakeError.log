Determining if the function pow exists in the m failed with the following output:
Change Dir: D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build/CMakeFiles/CMakeScratch/TryCompile-4cd9c1

Run Build Command(s):C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_e288c.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:m && 閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1



  CheckFunctionExists.c

LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥渕.lib鈥?[D:\DeepResearch\240924_Kafka_Flink_Rust_Html鏈€鏂版垚浜や环鐩戞祴@CLAW鏃ユ湰145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\CMakeFiles\CMakeScratch\TryCompile-4cd9c1\cmTC_e288c.vcxproj]




Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build/CMakeFiles/CMakeScratch/TryCompile-de4qrd

Run Build Command(s):C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_0f387.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:m && 閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1



  src.c

D:\DeepResearch\240924_Kafka_Flink_Rust_Html鏈€鏂版垚浜や环鐩戞祴@CLAW鏃ユ湰145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\CMakeFiles\CMakeScratch\TryCompile-de4qrd\src.c(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥減thread.h鈥? No such file or directory [D:\DeepResearch\240924_Kafka_Flink_Rust_Html鏈€鏂版垚浜や环鐩戞祴@CLAW鏃ユ湰145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\CMakeFiles\CMakeScratch\TryCompile-de4qrd\cmTC_0f387.vcxproj]



Source file was:
#include <pthread.h>

static void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_cancel(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}


Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build/CMakeFiles/CMakeScratch/TryCompile-qdgupy

Run Build Command(s):C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_e20cb.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:m && 閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1



  CheckFunctionExists.c

LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減threads.lib鈥?[D:\DeepResearch\240924_Kafka_Flink_Rust_Html鏈€鏂版垚浜や环鐩戞祴@CLAW鏃ユ湰145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\CMakeFiles\CMakeScratch\TryCompile-qdgupy\cmTC_e20cb.vcxproj]




Determining if the function pthread_create exists in the pthread failed with the following output:
Change Dir: D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build/CMakeFiles/CMakeScratch/TryCompile-lu4dgq

Run Build Command(s):C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_2ad73.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:m && 閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1



  CheckFunctionExists.c

LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減thread.lib鈥?[D:\DeepResearch\240924_Kafka_Flink_Rust_Html鏈€鏂版垚浜や环鐩戞祴@CLAW鏃ユ湰145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\CMakeFiles\CMakeScratch\TryCompile-lu4dgq\cmTC_2ad73.vcxproj]




