{"rustc": 1842507548689473721, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 2241668132362809309, "path": 9521196332118729153, "deps": [[4022439902832367970, "zerofrom_derive", false, 6121762085882525833]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerofrom-0fb446f38a0a687f\\dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}