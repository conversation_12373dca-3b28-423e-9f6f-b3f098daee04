{"rustc": 1842507548689473721, "features": "[\"proc-macro-crate\", \"std\"]", "declared_features": "[\"complex-expressions\", \"default\", \"external_doc\", \"proc-macro-crate\", \"std\"]", "target": 15019087522015688764, "profile": 2225463790103693989, "path": 13322498935687349181, "deps": [[3060637413840920116, "proc_macro2", false, 11068535599298301294], [4974441333307933176, "syn", false, 10417728577820579240], [15203748914246919255, "proc_macro_crate", false, 11114510411586497879], [17990358020177143287, "quote", false, 6671774948115873400]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\num_enum_derive-8bc9fd51760463cf\\dep-lib-num_enum_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}