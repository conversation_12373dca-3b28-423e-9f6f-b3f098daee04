﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{552DF33B-2A52-31B1-BCCC-C5517ECC68EB}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>rdkafka++</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\src-cpp\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">rdkafka++.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">rdkafka++</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\src-cpp\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">rdkafka++.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">rdkafka++</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\src-cpp\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">rdkafka++.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">rdkafka++</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\src-cpp\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">rdkafka++.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">rdkafka++</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp;C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/libz-sys-0f661d1bf475f385/out/include" -Brepro -Brepro</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>
      </ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WITHOUT_WIN32_CONFIG;WITH_SSL=0;WITH_ZLIB=1;WITH_SNAPPY=0;WITH_ZSTD=0;WITH_SASL_SCRAM=0;WITH_SASL_OAUTHBEARER=0;ENABLE_DEVEL=0;WITH_PLUGINS=1;LIBRDKAFKA_STATICLIB;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;WITHOUT_WIN32_CONFIG;WITH_SSL=0;WITH_ZLIB=1;WITH_SNAPPY=0;WITH_ZSTD=0;WITH_SASL_SCRAM=0;WITH_SASL_OAUTHBEARER=0;ENABLE_DEVEL=0;WITH_PLUGINS=1;LIBRDKAFKA_STATICLIB;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp;C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\libz-sys-0f661d1bf475f385\out\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp;C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\libz-sys-0f661d1bf475f385\out\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp;C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/libz-sys-0f661d1bf475f385/out/include" -Brepro</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>
      </ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);NDEBUG;WITHOUT_WIN32_CONFIG;WITH_SSL=0;WITH_ZLIB=1;WITH_SNAPPY=0;WITH_ZSTD=0;WITH_SASL_SCRAM=0;WITH_SASL_OAUTHBEARER=0;ENABLE_DEVEL=0;WITH_PLUGINS=1;LIBRDKAFKA_STATICLIB;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;NDEBUG;WITHOUT_WIN32_CONFIG;WITH_SSL=0;WITH_ZLIB=1;WITH_SNAPPY=0;WITH_ZSTD=0;WITH_SASL_SCRAM=0;WITH_SASL_OAUTHBEARER=0;ENABLE_DEVEL=0;WITH_PLUGINS=1;LIBRDKAFKA_STATICLIB;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp;C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\libz-sys-0f661d1bf475f385\out\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp;C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\libz-sys-0f661d1bf475f385\out\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp;C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/libz-sys-0f661d1bf475f385/out/include" -Brepro</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>
      </ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);NDEBUG;WITHOUT_WIN32_CONFIG;WITH_SSL=0;WITH_ZLIB=1;WITH_SNAPPY=0;WITH_ZSTD=0;WITH_SASL_SCRAM=0;WITH_SASL_OAUTHBEARER=0;ENABLE_DEVEL=0;WITH_PLUGINS=1;LIBRDKAFKA_STATICLIB;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;NDEBUG;WITHOUT_WIN32_CONFIG;WITH_SSL=0;WITH_ZLIB=1;WITH_SNAPPY=0;WITH_ZSTD=0;WITH_SASL_SCRAM=0;WITH_SASL_OAUTHBEARER=0;ENABLE_DEVEL=0;WITH_PLUGINS=1;LIBRDKAFKA_STATICLIB;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp;C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\libz-sys-0f661d1bf475f385\out\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp;C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\libz-sys-0f661d1bf475f385\out\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp;C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/libz-sys-0f661d1bf475f385/out/include" -Brepro</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>
      </ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);NDEBUG;WITHOUT_WIN32_CONFIG;WITH_SSL=0;WITH_ZLIB=1;WITH_SNAPPY=0;WITH_ZSTD=0;WITH_SASL_SCRAM=0;WITH_SASL_OAUTHBEARER=0;ENABLE_DEVEL=0;WITH_PLUGINS=1;LIBRDKAFKA_STATICLIB;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;NDEBUG;WITHOUT_WIN32_CONFIG;WITH_SSL=0;WITH_ZLIB=1;WITH_SNAPPY=0;WITH_ZSTD=0;WITH_SASL_SCRAM=0;WITH_SASL_OAUTHBEARER=0;ENABLE_DEVEL=0;WITH_PLUGINS=1;LIBRDKAFKA_STATICLIB;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp;C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\libz-sys-0f661d1bf475f385\out\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp;C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\generated\dummy;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\libz-sys-0f661d1bf475f385\out\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rdkafka-sys-4.9.0+2.10.0/librdkafka/src-cpp/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rdkafka-sys-4.9.0+2.10.0/librdkafka -BD:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build --check-stamp-file D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build/src-cpp/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\packaging\cmake\rdkafka.pc.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\src-cpp\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rdkafka-sys-4.9.0+2.10.0/librdkafka/src-cpp/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rdkafka-sys-4.9.0+2.10.0/librdkafka -BD:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build --check-stamp-file D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build/src-cpp/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\packaging\cmake\rdkafka.pc.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\src-cpp\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rdkafka-sys-4.9.0+2.10.0/librdkafka/src-cpp/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rdkafka-sys-4.9.0+2.10.0/librdkafka -BD:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build --check-stamp-file D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build/src-cpp/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\packaging\cmake\rdkafka.pc.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\src-cpp\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rdkafka-sys-4.9.0+2.10.0/librdkafka/src-cpp/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rdkafka-sys-4.9.0+2.10.0/librdkafka -BD:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build --check-stamp-file D:/DeepResearch/240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242/Flink_Rust_Trading/_3_web_rust/target/debug/build/rdkafka-sys-fcc90d097a12f73c/out/build/src-cpp/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\packaging\cmake\rdkafka.pc.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\src-cpp\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp\ConfImpl.cpp" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp\ConsumerImpl.cpp" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp\HandleImpl.cpp" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp\HeadersImpl.cpp" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp\KafkaConsumerImpl.cpp" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp\MessageImpl.cpp" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp\MetadataImpl.cpp" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp\ProducerImpl.cpp" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp\QueueImpl.cpp" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp\RdKafka.cpp" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp\TopicImpl.cpp" />
    <ClCompile Include="C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\rdkafka-sys-4.9.0+2.10.0\librdkafka\src-cpp\TopicPartitionImpl.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\ZERO_CHECK.vcxproj">
      <Project>{780C57D4-C24F-3321-9A34-FD92604DF167}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\rdkafka-sys-fcc90d097a12f73c\out\build\src\rdkafka.vcxproj">
      <Project>{D837848D-C883-3685-B315-330DCA02EDB4}</Project>
      <Name>rdkafka</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>