cargo:rerun-if-env-changed=LIBZ_SYS_STATIC
cargo:rerun-if-changed=build.rs
cargo:rerun-if-changed=zng/cmake.rs
cargo:rerun-if-changed=zng/cc.rs
note, vcpkg did not find zlib: Could not find Vcpkg tree: Could not find Vcpkg root at C:\Users\<USER>\Desktop\Telegram_API_Demo\build\vcpkg\.vcpkg-root
OPT_LEVEL = Some(0)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\deps;D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\Program Files\CMake\bin;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64;C:\Program Files\Python313\Scripts\;C:\Program Files\Python313\;C:\Program Files (x86)\oh-my-posh\bin\;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files\Bandizip\;C:\ProgramData\chocolatey\bin;C:\Program Files (x86)\GnuWin32\bin;C:\Program Files\PostgreSQL\16\bin;C:\Program Files\Redis\;D:\Restic;D:\ResticBackups;C:\Program Files (x86)\AOMEI\AOMEI Backupper\7.3.3;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Tailscale\;C:\Users\<USER>\.cargo\bin;C:;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.3.0\;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files\nodejs\;C:\Program Files\Apache\maven\apache-maven-3.9.9\bin;C:\Program Files\qemu;C:\Strawberry\perl\site\bin;C:\Strawberry\perl\bin;C:\Program Files\Go\bin;C:\Program Files\tdlib;C:\Program Files\moonbit;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;c:\Users\<USER>\AppData\Roaming\Python\python313\Scripts;C:\Users\<USER>\AppData\Local\pnpm\store\v3;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\.deno\bin;C:\Users\<USER>\AppData\Local\Pub\Cache\bin;C:\Program Files\CursorModifier;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;D:\Program\Xshell\NetSarang\Xmanager 8\;D:\Program\Xshell\NetSarang\Xshell 8\;D:\Program\Xshell\NetSarang\Xftp 8\;D:\Program\Xshell\NetSarang\Xlpd 8\;C:\Program Files\LLVM\bin;C:\Strawberry\c\bin;c:\Program Files\cursor\resources\app\bin;C:\Program Files\R\R-4.5.0\bin\x64;C:\Program Files\cursor\resources\app\bin;C:\Program Files\Git\cmd;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\scoop\apps\nodejs\current\bin;C:\Users\<USER>\scoop\apps\nodejs\current;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Programs\oh-my-posh\bin\;C:\Users\<USER>\.moon\bin;C:\Program Files\PowerShell\7;C:\Program Files\Python313\Scripts\;C:\Program Files\Python313\;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files\Bandizip\;C:\ProgramData\chocolatey\bin;C:\Program Files (x86)\GnuWin32\bin;C:\Program Files\Git\cmd;C:\Program Files\PostgreSQL\16\bin;C:\Program Files\Redis\;D:\Restic;D:\ResticBackups;C:\Program Files (x86)\AOMEI\AOMEI Backupper\7.3.3;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Tailscale\;C:;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.3.0\;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files\nodejs\;C:\Program Files\Apache\maven\apache-maven-3.9.9\bin;C:\Program Files\qemu;C:\Strawberry\c\bin;C:\Strawberry\perl\site\bin;C:\Strawberry\perl\bin;C:\Program Files\CMake\bin;C:\Program Files\WireGuard\;C:\Program Files\Go\bin;C:\Program Files\PowerShell\7\;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\tdlib;C:\Program Files\moonbit;c:\users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\Python\Python313\Scripts;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.deno\bin;C:\Users\<USER>\AppData\Local\SpacetimeDB;C:\flutter\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\Kiro\bin;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\bin)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = Some(C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\include;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Windows Kits\10\Include\10.0.22621.0\ucrt;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Windows Kits\10\Include\10.0.22621.0\um;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Windows Kits\10\Include\10.0.22621.0\shared)
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CC_x86_64-pc-windows-msvc
CC_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_x86_64_pc_windows_msvc
CC_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(true)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
adler32.c
compress.c
crc32.c
deflate.c
infback.c
inffast.c
inflate.c
inftrees.c
trees.c
uncompr.c
zutil.c
gzclose.c
gzlib.c
gzread.c
gzwrite.c
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-msvc
AR_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_msvc
AR_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_msvc
ARFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-msvc
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-search=native=C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64
cargo:rustc-link-lib=static=z
cargo:rustc-link-search=native=D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\libz-sys-0f661d1bf475f385\out\lib
cargo:root=D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\libz-sys-0f661d1bf475f385\out
cargo:rustc-link-search=native=D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\libz-sys-0f661d1bf475f385\out\lib
cargo:include=D:\DeepResearch\240924_Kafka_Flink_Rust_Html最新成交价监测@CLAW日本145.242\Flink_Rust_Trading\_3_web_rust\target\debug\build\libz-sys-0f661d1bf475f385\out/include
