{"rustc": 1842507548689473721, "features": "[\"cmake\", \"cmake-build\", \"libz\", \"libz-sys\"]", "declared_features": "[\"cmake\", \"cmake-build\", \"cmake_build\", \"curl\", \"curl-static\", \"curl-sys\", \"default\", \"dynamic-linking\", \"dynamic_linking\", \"external-lz4\", \"external_lz4\", \"gssapi\", \"gssapi-vendored\", \"libz\", \"libz-static\", \"libz-sys\", \"lz4-sys\", \"openssl-sys\", \"sasl\", \"sasl2-sys\", \"ssl\", \"ssl-vendored\", \"static-linking\", \"zstd\", \"zstd-pkg-config\", \"zstd-sys\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 11872667274786019664, "deps": [[3214373357989284387, "pkg_config", false, 7040452768402359941], [7499741813737603141, "cmake", false, 4538059790630044056]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rdkafka-sys-40b2b47e47255dec\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}